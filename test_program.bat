@echo off
echo 正在测试 DisableWindowsUpdate 程序...
echo.
echo 注意：此程序需要管理员权限才能正常运行
echo 程序将在后台运行，不会显示控制台窗口
echo 日志文件位置：%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log
echo.
echo 按任意键启动程序...
pause > nul

echo 启动程序...
start "" "bin\Release\DisableWindowsUpdate.exe"

echo.
echo 程序已启动（后台运行）
echo 请检查日志文件以查看程序运行状态
echo 日志文件路径：%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log
echo.
echo 要查看日志文件，请按任意键...
pause > nul

if exist "%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log" (
    echo 打开日志文件...
    notepad "%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log"
) else (
    echo 日志文件尚未创建，程序可能需要一些时间来初始化
    echo 请稍后手动检查：%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log
)

echo.
echo 测试完成
pause
