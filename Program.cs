﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Reflection;

namespace DisableWindowsUpdate
{
    internal class Program
    {
        private static bool isRunning = true;
        private static readonly string[] updateServices = new string[] 
        {
            "wuauserv",        // Windows Update 服务
            //"WaaSMedicSvc",    // Windows Update Medic 服务
            "UsoSvc",          // 更新编排器服务
            "BITS",            // 后台智能传输服务（用于下载更新）//Background Intelligent Transfer Service
            //"dosvc"            // 分发优化服务（用于下载更新）
        };

        static void Main(string[] args)
        {
            // 设置应用程序为单实例运行
            bool createdNew;
            using (var mutex = new Mutex(true, "DisableWindowsUpdateApp", out createdNew))
            {
                if (!createdNew)
                {
                    // 如果应用程序已经在运行，则直接退出
                    LogMessage("程序已经在运行中，退出本实例");
                    return;
                }

                // 记录启动日志
                LogMessage("Windows更新禁用程序已启动");
                
                try
                {
                    // 显示欢迎信息
                    Console.WriteLine("=================================================");
                    Console.WriteLine("        Windows 更新禁用程序 - 控制台版本        ");
                    Console.WriteLine("=================================================");
                    Console.WriteLine("1. 立即禁用所有更新组件");
                    Console.WriteLine("2. 设置/取消开机自启动");
                    Console.WriteLine("3. 退出程序");
                    Console.WriteLine("=================================================");
                    Console.WriteLine("程序正在后台监控Windows更新服务...");
                    
                    // 设置开机自启动
                    SetStartup();
                    
                    // 立即执行一次全面禁用
                    DisableAllWindowsUpdateComponents();

                    // 启动后台检测线程
                    Thread monitorThread = new Thread(new ThreadStart(() => {
                        MonitorWindowsUpdateServices();
                    }));
                    monitorThread.IsBackground = true;
                    monitorThread.Start();

                    // 控制台交互循环
                    while (isRunning)
                    {
                        Console.Write("\n请输入选项 (1-3): ");
                        string input = Console.ReadLine();
                        
                        switch (input)
                        {
                            case "1":
                                DisableAllWindowsUpdateComponents();
                                Console.WriteLine("已禁用所有Windows更新组件");
                                break;
                            case "2":
                                bool currentStatus = IsStartupEnabled();
                                ToggleStartup();
                                string newStatus = IsStartupEnabled() ? "已启用" : "已禁用";
                                Console.WriteLine($"开机自启动: {newStatus}");
                                break;
                            case "3":
                                isRunning = false;
                                LogMessage("用户请求退出程序");
                                break;
                            default:
                                Console.WriteLine("无效的选项，请重新输入");
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"程序发生错误: {ex.Message}");
                }
                
                LogMessage("Windows更新禁用程序已停止");
            }
        }

        static void MonitorWindowsUpdateServices()
        {
            while (isRunning)
            {
                try
                {
                    bool changesDetected = false;
                    
                    // 检查服务状态并禁用
                    changesDetected |= CheckAndDisableWindowsUpdateServices();
                    
                    // 仅在检测到变化时应用其他设置
                    if (changesDetected)
                    {
                        ApplyRegistrySettings();
                        DisableWindowsUpdateTasks();
                        LogMessage("检测到Windows更新服务被重新启用，已自动禁用");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"监控线程发生错误: {ex.Message}");
                }
                
                // 每30秒检查一次
                Thread.Sleep(30000);
            }
        }

        static void SetStartup()
        {
            try
            {
                string appPath = Assembly.GetExecutingAssembly().Location;
                
                // 添加到注册表的启动项中
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", true))
                {
                    // 检查是否已存在启动项
                    if (key.GetValue("DisableWindowsUpdate") == null)
                    {
                        key.SetValue("DisableWindowsUpdate", appPath);
                        LogMessage("已设置程序开机自启动");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"设置开机自启动时发生错误: {ex.Message}");
            }
        }

        static bool IsStartupEnabled()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", false))
                {
                    return key.GetValue("DisableWindowsUpdate") != null;
                }
            }
            catch
            {
                return false;
            }
        }

        static void ToggleStartup()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", true))
                {
                    if (key.GetValue("DisableWindowsUpdate") == null)
                    {
                        // 添加启动项
                        string appPath = Assembly.GetExecutingAssembly().Location;
                        key.SetValue("DisableWindowsUpdate", appPath);
                        LogMessage("已启用开机自启动");
                    }
                    else
                    {
                        // 移除启动项
                        key.DeleteValue("DisableWindowsUpdate", false);
                        LogMessage("已禁用开机自启动");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"切换开机自启动设置时发生错误: {ex.Message}");
            }
        }

        static void DisableAllWindowsUpdateComponents()
        {
            LogMessage("执行全面禁用 Windows 更新组件操作");
            
            // 禁用服务
            foreach (string service in updateServices)
            {
                DisableService(service);
            }
            
            // 应用注册表设置
            ApplyRegistrySettings();
            
            // 禁用计划任务
            DisableWindowsUpdateTasks();
            
            LogMessage("完成全面禁用 Windows 更新组件操作");
        }

        static bool CheckAndDisableWindowsUpdateServices()
        {
            bool changesMade = false; // 标记本轮是否有成功执行的操作
            bool permissionErrorOccurred = false; // 标记本轮是否有权限错误

            foreach (string serviceName in updateServices)
            {
                try
                {
                    if (!ServiceExists(serviceName))
                    {
                        continue;
                    }
                    
                    ServiceController service = new ServiceController(serviceName);
                    
                    // 检查服务是否处于运行状态
                    if (service.Status == ServiceControllerStatus.Running)
                    {
                        LogMessage($"检测到服务 {serviceName} 正在运行，正在尝试停止...");
                        if (RunCommandAsAdmin("net", $"stop {serviceName}"))
                        {
                            changesMade = true;
                        }
                        else
                        {
                            // 停止失败，可能是权限问题
                            LogMessage($"停止服务 {serviceName} 失败 (可能由于权限不足)");
                            permissionErrorOccurred = true; 
                        }
                    }
                    
                    // 检查服务启动类型是否不是禁用
                    string startType = GetServiceStartType(serviceName);
                    if (startType != "DISABLED")
                    {
                        LogMessage($"检测到服务 {serviceName} 启动类型为 {startType}，正在尝试设置为禁用...");
                        if (RunCommandAsAdmin("sc", $"config {serviceName} start= disabled"))
                        {
                             changesMade = true;
                        }
                         else
                        {
                            // 配置失败，可能是权限问题
                            LogMessage($"配置服务 {serviceName} 为禁用失败 (可能由于权限不足)");
                            permissionErrorOccurred = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"检查服务 {serviceName} 时发生错误: {ex.Message}");
                }
            }

            // 如果本轮检测中有权限错误发生
            if (permissionErrorOccurred) {
                LogMessage("检测到权限错误，部分服务可能无法被禁用。请以管理员身份运行程序。");
            }
            
            // 返回是否有成功执行的操作
            return changesMade; 
        }

        static void DisableService(string serviceName)
        {
            try
            {
                // 检查服务是否存在
                if (ServiceExists(serviceName))
                {
                    ServiceController service = new ServiceController(serviceName);
                    
                    // 检查服务是否处于运行状态
                    if (service.Status == ServiceControllerStatus.Running)
                    {
                        LogMessage($"正在停止服务 {serviceName}...");
                        RunCommandAsAdmin("net", $"stop {serviceName}");
                    }
                    
                    // 设置服务为禁用状态
                    LogMessage($"正在禁用服务 {serviceName}...");
                    RunCommandAsAdmin("sc", $"config {serviceName} start= disabled");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"禁用服务 {serviceName} 时发生错误: {ex.Message}");
            }
        }

        static bool ServiceExists(string serviceName)
        {
            try
            {
                ServiceController[] services = ServiceController.GetServices();
                return services.Any(s => s.ServiceName.Equals(serviceName, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        static string GetServiceStartType(string serviceName)
        {
            try
            {
                Process process = new Process();
                process.StartInfo = new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = $"qc {serviceName}",
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                
                process.Start();
                string output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                
                // 解析输出以获取启动类型
                foreach (string line in output.Split('\n'))
                {
                    if (line.Trim().StartsWith("START_TYPE"))
                    {
                        if (line.Contains("DISABLED"))
                            return "DISABLED";
                        else if (line.Contains("AUTO_START"))
                            return "AUTO_START";
                        else if (line.Contains("DEMAND_START"))
                            return "DEMAND_START";
                        else 
                            return line.Trim();
                    }
                }
                
                return "UNKNOWN";
            }
            catch (Exception ex)
            {
                LogMessage($"获取服务 {serviceName} 启动类型时发生错误: {ex.Message}");
                return "ERROR";
            }
        }

        static void ApplyRegistrySettings()
        {
            try
            {
                LogMessage("正在应用注册表设置以禁用 Windows 更新...");
                
                // 禁用 Windows 自动更新
                using (RegistryKey key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"))
                {
                    key.SetValue("NoAutoUpdate", 1, RegistryValueKind.DWord);
                    key.SetValue("AUOptions", 1, RegistryValueKind.DWord);
                    key.SetValue("UseWUServer", 1, RegistryValueKind.DWord);
                }
                
                // 禁用更新下载
                using (RegistryKey key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"))
                {
                    key.SetValue("DoNotConnectToWindowsUpdateInternetLocations", 1, RegistryValueKind.DWord);
                    key.SetValue("DisableWindowsUpdateAccess", 1, RegistryValueKind.DWord);
                }
                
                // 禁用更新助手
                using (RegistryKey key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Microsoft\WindowsUpdate\UX\Settings"))
                {
                    key.SetValue("UxOption", 0, RegistryValueKind.DWord);
                }
                
                // 配置 Windows 更新服务器为无效地址
                using (RegistryKey key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"))
                {
                    key.SetValue("WUServer", "http://127.0.0.1:8888", RegistryValueKind.String);
                    key.SetValue("WUStatusServer", "http://127.0.0.1:8888", RegistryValueKind.String);
                }
                
                LogMessage("注册表设置已成功应用");
            }
            catch (Exception ex)
            {
                LogMessage($"应用注册表设置时发生错误: {ex.Message}");
            }
        }

        static void DisableWindowsUpdateTasks()
        {
            try
            {
                LogMessage("正在禁用 Windows 更新相关计划任务...");
                
                string[] updateTasks = new string[]
                {
                    @"\Microsoft\Windows\WindowsUpdate\Automatic App Update",
                    @"\Microsoft\Windows\WindowsUpdate\Scheduled Start",
                    @"\Microsoft\Windows\UpdateOrchestrator\Schedule Scan",
                    @"\Microsoft\Windows\UpdateOrchestrator\UUS Scan",
                    @"\Microsoft\Windows\UpdateOrchestrator\Maintenance Install",
                    @"\Microsoft\Windows\UpdateOrchestrator\UpdateModelTask",
                    @"\Microsoft\Windows\UpdateOrchestrator\USO_UxBroker",
                    @"\Microsoft\Windows\WindowsUpdate\sihboot"
                };
                
                foreach (string task in updateTasks)
                {
                    try
                    {
                        RunCommandAsAdmin("schtasks", $"/Change /TN \"{task}\" /Disable");
                        LogMessage($"已禁用任务: {task}");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"禁用任务 {task} 时发生错误: {ex.Message}");
                    }
                }
                
                LogMessage("更新计划任务禁用操作完成");
            }
            catch (Exception ex)
            {
                LogMessage($"禁用计划任务时发生错误: {ex.Message}");
            }
        }

        static bool RunCommandAsAdmin(string command, string arguments)
        {
            try
            {
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = arguments,
                    Verb = "runas", // 以管理员身份运行
                    UseShellExecute = true,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden // 尝试隐藏可能弹出的窗口
                };
                
                Process process = Process.Start(psi);
                process.WaitForExit();
                
                // 检查退出代码，0 通常表示成功
                if (process.ExitCode == 0)
                {
                    LogMessage($"成功执行命令: {command} {arguments}");
                    return true;
                }
                else 
                {
                     LogMessage($"执行命令失败: {command} {arguments}, 退出代码: {process.ExitCode}");
                     return false;
                }
            }
            catch (Exception ex)
            {
                // 特别处理拒绝访问的异常 (System.ComponentModel.Win32Exception)
                // Access Denied HResult is -2147467259 (0x80004005) or NativeErrorCode 5
                if (ex is System.ComponentModel.Win32Exception win32Ex && (win32Ex.NativeErrorCode == 5 || win32Ex.HResult == -2147467259)) 
                {
                     LogMessage($"执行命令 {command} {arguments} 时被拒绝访问: {ex.Message}");
                } else {
                     LogMessage($"执行命令 {command} {arguments} 时发生错误: {ex.Message}");
                }
                // 这里不再抛出异常，而是返回 false，让调用者知道操作失败
                return false; 
            }
        }

        static void LogMessage(string message)
        {
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
            
            // 直接输出到控制台，不再写入日志文件
            Console.WriteLine(logEntry);
        }
    }
}
