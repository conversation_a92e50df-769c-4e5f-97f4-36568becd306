# Windows 更新禁用程序 - 后台版本

## 概述

这是一个完全后台运行的 Windows 更新禁用程序，无控制台窗口，自动监控并禁用 Windows 更新服务。

## 主要特性

- **完全隐藏运行**：程序运行时不显示任何窗口，完全在后台运行
- **自动开机启动**：程序会自动设置开机自启动
- **持续监控**：每30秒检查一次 Windows 更新服务状态，自动禁用被重新启用的服务
- **日志记录**：所有操作都记录到日志文件中
- **单实例运行**：防止重复运行多个实例

## 禁用的服务

程序会禁用以下 Windows 更新相关服务：
- `wuauserv` - Windows Update 服务
- `UsoSvc` - 更新编排器服务
- `BITS` - 后台智能传输服务

## 日志文件位置

日志文件保存在：`%APPDATA%\DisableWindowsUpdate\DisableWindowsUpdate.log`

完整路径通常为：`C:\Users\<USER>\AppData\Roaming\DisableWindowsUpdate\DisableWindowsUpdate.log`

## 使用方法

1. **以管理员身份运行**：右键点击 `DisableWindowsUpdate.exe`，选择"以管理员身份运行"
2. **程序会自动**：
   - 设置开机自启动
   - 立即禁用所有 Windows 更新组件
   - 开始后台监控
3. **查看日志**：打开日志文件查看程序运行状态

## 测试程序

运行 `test_program.bat` 可以快速测试程序功能并查看日志。

## 停止程序

要停止程序，可以通过以下方式：
1. 打开任务管理器，找到 `DisableWindowsUpdate.exe` 进程并结束
2. 或者重启计算机

## 卸载程序

1. 结束程序进程
2. 删除开机自启动项：
   - 按 `Win+R`，输入 `msconfig`
   - 在"启动"选项卡中找到并禁用 `DisableWindowsUpdate`
3. 删除程序文件和日志文件

## 注意事项

- **需要管理员权限**：程序必须以管理员身份运行才能正常工作
- **系统兼容性**：适用于 Windows 10/11 系统
- **安全软件**：某些安全软件可能会阻止程序运行，需要添加到白名单
- **系统更新**：使用此程序会完全禁用 Windows 自动更新，请定期手动检查重要的安全更新

## 技术细节

程序通过以下方式禁用 Windows 更新：
1. 停止并禁用相关系统服务
2. 修改注册表设置
3. 禁用相关的计划任务
4. 持续监控并自动重新应用设置

## 故障排除

如果程序无法正常工作：
1. 确保以管理员身份运行
2. 检查日志文件中的错误信息
3. 确保没有被安全软件阻止
4. 尝试重新启动计算机后再次运行
