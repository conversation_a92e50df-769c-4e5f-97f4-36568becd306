@echo off
echo 正在停止 DisableWindowsUpdate 程序...
echo.

:: 检查是否有管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 警告：建议以管理员身份运行此脚本以确保完全停止程序
    echo.
)

:: 尝试结束进程
echo 正在查找并结束 DisableWindowsUpdate.exe 进程...
taskkill /f /im DisableWindowsUpdate.exe >nul 2>&1

if %errorLevel% == 0 (
    echo 程序已成功停止
) else (
    echo 未找到正在运行的 DisableWindowsUpdate.exe 进程
)

echo.
echo 检查开机自启动设置...

:: 检查注册表中的启动项
reg query "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v DisableWindowsUpdate >nul 2>&1
if %errorLevel% == 0 (
    echo 发现开机自启动项，是否要删除？ (Y/N)
    set /p choice=请选择: 
    if /i "%choice%"=="Y" (
        reg delete "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v DisableWindowsUpdate /f >nul 2>&1
        if %errorLevel% == 0 (
            echo 开机自启动项已删除
        ) else (
            echo 删除开机自启动项失败，请手动删除
        )
    ) else (
        echo 保留开机自启动项
    )
) else (
    echo 未发现开机自启动项
)

echo.
echo 操作完成
pause
